import React, { useState } from 'react';
import { useDesigner } from '../../context/DesignerContext';
import { createDefaultComponentSchema } from '@lowcode/renderer';

export interface DesignComponentSidebarProps {
  className?: string;
  style?: React.CSSProperties;
}

// 组件数据定义
interface ComponentItem {
  key: string;
  label: string;
  icon: string;
}

interface TabData {
  key: string;
  label: string;
  icon: string;
  items: ComponentItem[];
}

const tabsData: TabData[] = [
  {
    key: 'layout',
    label: '布局',
    icon: 'view_quilt',
    items: [
      { key: 'Container', label: '容器', icon: 'view_quilt' }
    ]
  },
  {
    key: 'basics',
    label: '基础',
    icon: 'widgets',
    items: [
      { key: 'Text', label: '文本', icon: 'text_fields' },
      { key: 'Button', label: '按钮', icon: 'smart_button' },
      { key: 'Input', label: '输入框', icon: 'input' },
      { key: 'Image', label: '图片', icon: 'image' }
    ]
  },
  {
    key: 'business',
    label: '业务',
    icon: 'business_center',
    items: [
      { key: 'TableViewWithSearch', label: '表格', icon: 'table_chart' }
    ]
  }
];

export const DesignComponentSidebar: React.FC<DesignComponentSidebarProps> = ({
  className,
  style
}) => {
  const { addComponent, canvasState } = useDesigner();
  const [activeTab, setActiveTab] = useState('basics');
  const isCollapsed = canvasState.componentPanelCollapsed;

  // 处理组件点击
  const handleComponentClick = (componentKey: string) => {
    const schema = createDefaultComponentSchema(componentKey);
    addComponent(schema);
  };

  // 渲染组件项
  const renderComponentItem = (item: ComponentItem) => (
    <div
      key={item.key}
      className="component-item"
      onClick={() => handleComponentClick(item.key)}
      style={{
        padding: '8px',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        textAlign: 'center',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        backgroundColor: '#ffffff',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f8fafc';
        e.currentTarget.style.borderColor = '#3b82f6';
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.15)';
        e.currentTarget.style.transform = 'translateY(-2px)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#ffffff';
        e.currentTarget.style.borderColor = '#e5e7eb';
        e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        e.currentTarget.style.transform = 'translateY(0)';
      }}
    >
      <span
        className="material-icons"
        style={{
          fontSize: '28px',
          color: '#6b7280',
          display: 'block',
          marginBottom: '4px'
        }}
      >
        {item.icon}
      </span>
      <p style={{
        fontSize: '10px',
        color: '#1f2937',
        margin: 0,
        fontWeight: '600',
        lineHeight: '1.2'
      }}>
        {item.label}
      </p>
    </div>
  );

  // 渲染基础组件分组
  const renderBasicsContent = () => {
    const basicsTab = tabsData.find(tab => tab.key === 'basics');
    if (!basicsTab) return null;

    return (
      <div style={{ padding: '6px' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gap: '4px'
        }}>
          {basicsTab.items.map(renderComponentItem)}
        </div>
      </div>
    );
  };

  // 渲染标签页内容
  const renderTabContent = () => {
    const currentTab = tabsData.find(tab => tab.key === activeTab);
    if (!currentTab) return null;

    if (activeTab === 'basics') {
      return renderBasicsContent();
    }

    return (
      <div style={{ padding: '6px' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gap: '4px'
        }}>
          {currentTab.items.map(renderComponentItem)}
        </div>
      </div>
    );
  };

  return (
    <aside
      className={className}
      style={{
        width: isCollapsed ? '0px' : '152px',
        backgroundColor: '#ffffff',
        borderRight: isCollapsed ? 'none' : '1px solid #e5e7eb',
        display: 'flex',
        flexDirection: 'column',
        flexShrink: 0,
        transition: 'all 0.3s ease',
        position: 'relative',
        overflow: 'hidden',
        boxShadow: isCollapsed ? 'none' : '2px 0 8px rgba(0, 0, 0, 0.1)',
        ...style
      }}
    >

      {/* 侧边栏内容 */}
      <div 
        style={{
          opacity: isCollapsed ? 0 : 1,
          transition: 'opacity 0.3s',
          display: 'flex',
          flex: 1,
          overflow: 'hidden'
        }}
      >
        {/* 左侧标签栏 */}
        <div style={{
          width: '48px',
          backgroundColor: '#f8fafc',
          borderRight: '1px solid #e5e7eb',
          flexShrink: 0,
          display: 'flex',
          flexDirection: 'column'
        }}>
          <nav style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '6px',
            padding: '12px 4px'
          }}>
            {tabsData.map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  padding: '10px 4px',
                  borderRadius: '8px',
                  border: 'none',
                  backgroundColor: activeTab === tab.key ? '#3b82f6' : 'transparent',
                  color: activeTab === tab.key ? '#ffffff' : '#6b7280',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  position: 'relative',
                  boxShadow: activeTab === tab.key ? '0 2px 4px rgba(59, 130, 246, 0.3)' : 'none'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== tab.key) {
                    e.currentTarget.style.backgroundColor = '#f1f5f9';
                    e.currentTarget.style.color = '#374151';
                    e.currentTarget.style.transform = 'scale(1.05)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== tab.key) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#6b7280';
                    e.currentTarget.style.transform = 'scale(1)';
                  }
                }}
              >
                <span className="material-icons" style={{
                  fontSize: '18px',
                  marginBottom: '2px'
                }}>
                  {tab.icon}
                </span>
                <span style={{
                  fontSize: '9px',
                  fontWeight: '600',
                  letterSpacing: '0.5px'
                }}>
                  {tab.label}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* 右侧内容区 */}
        <div style={{
          flex: 1,
          overflowY: 'auto',
          transition: 'opacity 0.3s ease',
          backgroundColor: '#ffffff',
          padding: '8px'
        }}>
          {renderTabContent()}
        </div>
      </div>
    </aside>
  );
};
