import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { PageSchema, ComponentSchema } from '@lowcode/renderer';
import { DesignerContext as IDesignerContext, CanvasState, HistoryState } from '../types';

// 设计器状态
interface DesignerState {
  schema: PageSchema;
  canvasState: CanvasState;
  history: HistoryState[];
  currentHistoryIndex: number;
}

// 设计器动作
type DesignerAction =
  | { type: 'UPDATE_SCHEMA'; payload: PageSchema }
  | { type: 'SELECT_COMPONENT'; payload: string | undefined }
  | { type: 'HOVER_COMPONENT'; payload: string | undefined }
  | { type: 'ADD_COMPONENT'; payload: { component: ComponentSchema; parentId?: string; index?: number } }
  | { type: 'REMOVE_COMPONENT'; payload: string }
  | { type: 'UPDATE_COMPONENT'; payload: { id: string; updates: Partial<ComponentSchema> } }
  | { type: 'SET_CANVAS_MODE'; payload: 'design' | 'preview' }
  | { type: 'SET_CANVAS_SCALE'; payload: number }
  | { type: 'SET_CANVAS_OFFSET'; payload: { x: number; y: number } }
  | { type: 'TOGGLE_COMPONENT_PANEL' }
  | { type: 'UNDO' }
  | { type: 'REDO' }
  | { type: 'SAVE_HISTORY'; payload: { description: string } };

// 从localStorage读取组件库收缩状态
const getInitialComponentPanelState = (): boolean => {
  try {
    const saved = localStorage.getItem('lowcode-designer-component-panel-collapsed');
    return saved ? JSON.parse(saved) : false;
  } catch {
    return false;
  }
};

// 初始状态
const initialState: DesignerState = {
  schema: {
    id: 'page_1',
    title: '新页面',
    components: [],
    apis: [],
    theme: {},
    layout: { type: 'admin', header: true, sidebar: true, footer: true }
  },
  canvasState: {
    selectedComponentId: undefined,
    hoveredComponentId: undefined,
    scale: 1,
    offset: { x: 0, y: 0 },
    mode: 'design',
    componentPanelCollapsed: getInitialComponentPanelState()
  },
  history: [],
  currentHistoryIndex: -1
};

// 工具函数：生成唯一ID
const generateId = (prefix: string = 'comp') => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 工具函数：深度克隆
const deepClone = <T,>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj as T;
  if (obj instanceof Date) return new Date(obj.getTime()) as any;
  if (Array.isArray(obj)) return (obj as any[]).map(item => deepClone(item)) as any;
  const cloned: any = {};
  Object.keys(obj as any).forEach((key) => {
    cloned[key] = deepClone((obj as any)[key]);
  });
  return cloned as T;
};

// 工具函数：查找组件
const findComponent = (components: ComponentSchema[], id: string): ComponentSchema | null => {
  for (const component of components) {
    if (component.id === id) return component;
    if (component.children) {
      const found = findComponent(component.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 工具函数：添加组件
const addComponentToSchema = (
  components: ComponentSchema[],
  newComponent: ComponentSchema,
  parentId?: string,
  index?: number
): ComponentSchema[] => {
  if (!parentId) {
    // 添加到根级别
    const newComponents = [...components];
    if (index !== undefined) {
      newComponents.splice(index, 0, newComponent);
    } else {
      newComponents.push(newComponent);
    }
    return newComponents;
  }

  // 添加到指定父组件
  return components.map(component => {
    if (component.id === parentId) {
      const children = component.children || [];
      const newChildren = [...children];
      if (index !== undefined) {
        newChildren.splice(index, 0, newComponent);
      } else {
        newChildren.push(newComponent);
      }
      return { ...component, children: newChildren };
    }

    if (component.children) {
      return {
        ...component,
        children: addComponentToSchema(component.children, newComponent, parentId, index)
      };
    }

    return component;
  });
};

// 工具函数：移除组件
const removeComponentFromSchema = (components: ComponentSchema[], id: string): ComponentSchema[] => {
  return components.filter(component => {
    if (component.id === id) return false;
    if (component.children) {
      component.children = removeComponentFromSchema(component.children, id);
    }
    return true;
  });
};

// 工具函数：更新组件
const updateComponentInSchema = (
  components: ComponentSchema[],
  id: string,
  updates: Partial<ComponentSchema>
): ComponentSchema[] => {
  return components.map(component => {
    if (component.id === id) {
      return { ...component, ...updates };
    }
    if (component.children) {
      return {
        ...component,
        children: updateComponentInSchema(component.children, id, updates)
      };
    }
    return component;
  });
};

// Reducer函数
const designerReducer = (state: DesignerState, action: DesignerAction): DesignerState => {
  switch (action.type) {
    case 'UPDATE_SCHEMA':
      return {
        ...state,
        schema: action.payload,
        history: [
          ...state.history.slice(0, state.currentHistoryIndex + 1),
          {
            schema: deepClone(action.payload),
            timestamp: Date.now(),
            description: '更新页面Schema'
          }
        ],
        currentHistoryIndex: state.currentHistoryIndex + 1
      };

    case 'SELECT_COMPONENT':
      return {
        ...state,
        canvasState: {
          ...state.canvasState,
          selectedComponentId: action.payload
        }
      };

    case 'HOVER_COMPONENT':
      return {
        ...state,
        canvasState: {
          ...state.canvasState,
          hoveredComponentId: action.payload
        }
      };

    case 'ADD_COMPONENT':
      const newComponents = addComponentToSchema(
        state.schema.components,
        action.payload.component,
        action.payload.parentId,
        action.payload.index
      );
      const newSchema = { ...state.schema, components: newComponents };
      return {
        ...state,
        schema: newSchema,
        // 不自动选中新添加的组件，让用户手动点击预览区来选中
        canvasState: {
          ...state.canvasState,
          // selectedComponentId: action.payload.component.id  // 注释掉自动选中
        },
        history: [
          ...state.history.slice(0, state.currentHistoryIndex + 1),
          {
            schema: deepClone(newSchema),
            timestamp: Date.now(),
            description: `添加组件: ${action.payload.component.type}`
          }
        ],
        currentHistoryIndex: state.currentHistoryIndex + 1
      };

    case 'REMOVE_COMPONENT':
      const componentsAfterRemove = removeComponentFromSchema(state.schema.components, action.payload);
      const schemaAfterRemove = { ...state.schema, components: componentsAfterRemove };
      return {
        ...state,
        schema: schemaAfterRemove,
        canvasState: {
          ...state.canvasState,
          selectedComponentId: state.canvasState.selectedComponentId === action.payload
            ? undefined
            : state.canvasState.selectedComponentId
        },
        history: [
          ...state.history.slice(0, state.currentHistoryIndex + 1),
          {
            schema: deepClone(schemaAfterRemove),
            timestamp: Date.now(),
            description: `删除组件: ${action.payload}`
          }
        ],
        currentHistoryIndex: state.currentHistoryIndex + 1
      };

    case 'UPDATE_COMPONENT':
      const componentsAfterUpdate = updateComponentInSchema(
        state.schema.components,
        action.payload.id,
        action.payload.updates
      );
      const schemaAfterUpdate = { ...state.schema, components: componentsAfterUpdate };
      return {
        ...state,
        schema: schemaAfterUpdate,
        history: [
          ...state.history.slice(0, state.currentHistoryIndex + 1),
          {
            schema: deepClone(schemaAfterUpdate),
            timestamp: Date.now(),
            description: `更新组件: ${action.payload.id}`
          }
        ],
        currentHistoryIndex: state.currentHistoryIndex + 1
      };

    case 'SET_CANVAS_MODE':
      return {
        ...state,
        canvasState: {
          ...state.canvasState,
          mode: action.payload
        }
      };

    case 'SET_CANVAS_SCALE':
      return {
        ...state,
        canvasState: {
          ...state.canvasState,
          scale: action.payload
        }
      };

    case 'SET_CANVAS_OFFSET':
      return {
        ...state,
        canvasState: {
          ...state.canvasState,
          offset: action.payload
        }
      };

    case 'TOGGLE_COMPONENT_PANEL':
      const newCollapsedState = !state.canvasState.componentPanelCollapsed;
      // 保存状态到localStorage
      try {
        localStorage.setItem('lowcode-designer-component-panel-collapsed', JSON.stringify(newCollapsedState));
      } catch (error) {
        console.warn('Failed to save component panel state to localStorage:', error);
      }
      return {
        ...state,
        canvasState: {
          ...state.canvasState,
          componentPanelCollapsed: newCollapsedState
        }
      };

    case 'UNDO':
      if (state.currentHistoryIndex > 0) {
        const prevHistoryIndex = state.currentHistoryIndex - 1;
        const prevHistory = state.history[prevHistoryIndex];
        return {
          ...state,
          schema: deepClone(prevHistory.schema),
          currentHistoryIndex: prevHistoryIndex
        };
      }
      return state;

    case 'REDO':
      if (state.currentHistoryIndex < state.history.length - 1) {
        const nextHistoryIndex = state.currentHistoryIndex + 1;
        const nextHistory = state.history[nextHistoryIndex];
        return {
          ...state,
          schema: deepClone(nextHistory.schema),
          currentHistoryIndex: nextHistoryIndex
        };
      }
      return state;

    case 'SAVE_HISTORY':
      return {
        ...state,
        history: [
          ...state.history.slice(0, state.currentHistoryIndex + 1),
          {
            schema: deepClone(state.schema),
            timestamp: Date.now(),
            description: action.payload.description
          }
        ],
        currentHistoryIndex: state.currentHistoryIndex + 1
      };

    default:
      return state;
  }
};

// Context创建
const DesignerContext = createContext<IDesignerContext | null>(null);

// Provider组件
interface DesignerProviderProps {
  children: ReactNode;
  initialSchema?: PageSchema;
}

export const DesignerProvider: React.FC<DesignerProviderProps> = ({
  children,
  initialSchema
}) => {
  const [state, dispatch] = useReducer(designerReducer, {
    ...initialState,
    schema: initialSchema || initialState.schema
  });

  const contextValue: IDesignerContext = {
    schema: state.schema,
    canvasState: state.canvasState,
    history: state.history,
    currentHistoryIndex: state.currentHistoryIndex,

    updateSchema: (schema: PageSchema) => {
      dispatch({ type: 'UPDATE_SCHEMA', payload: schema });
    },

    selectComponent: (id?: string) => {
      dispatch({ type: 'SELECT_COMPONENT', payload: id });
    },

    hoverComponent: (id?: string) => {
      dispatch({ type: 'HOVER_COMPONENT', payload: id });
    },

    addComponent: (component: ComponentSchema, parentId?: string, index?: number) => {
      // 确保组件有唯一ID
      if (!component.id) {
        component.id = generateId(component.type);
      }
      dispatch({
        type: 'ADD_COMPONENT',
        payload: { component, parentId, index }
      });
    },

    removeComponent: (id: string) => {
      dispatch({ type: 'REMOVE_COMPONENT', payload: id });
    },

    updateComponent: (id: string, updates: Partial<ComponentSchema>) => {
      dispatch({
        type: 'UPDATE_COMPONENT',
        payload: { id, updates }
      });
    },

    undo: () => {
      dispatch({ type: 'UNDO' });
    },

    redo: () => {
      dispatch({ type: 'REDO' });
    },

    setCanvasMode: (mode: 'design' | 'preview') => {
      dispatch({ type: 'SET_CANVAS_MODE', payload: mode });
    },

    setCanvasScale: (scale: number) => {
      dispatch({ type: 'SET_CANVAS_SCALE', payload: scale });
    },

    setCanvasOffset: (offset: { x: number; y: number }) => {
      dispatch({ type: 'SET_CANVAS_OFFSET', payload: offset });
    },

    toggleComponentPanel: () => {
      dispatch({ type: 'TOGGLE_COMPONENT_PANEL' });
    }
  };

  return (
    <DesignerContext.Provider value={contextValue}>
      {children}
    </DesignerContext.Provider>
  );
};

// Hook
export const useDesigner = (): IDesignerContext => {
  const context = useContext(DesignerContext);
  if (!context) {
    throw new Error('useDesigner must be used within a DesignerProvider');
  }
  return context;
};
