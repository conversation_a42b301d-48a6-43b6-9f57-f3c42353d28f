import React from 'react';
export interface TableColumn {
    key: string;
    title: string;
    dataIndex: string;
    width?: number | string;
    align?: 'left' | 'center' | 'right';
    sortable?: boolean;
    filterable?: boolean;
    render?: (value: any, record: any, index: number) => React.ReactNode;
}
export interface TableAction {
    key: string;
    label: string;
    icon?: string;
    type?: 'primary' | 'default' | 'danger';
    disabled?: boolean;
    onClick?: (record: any, index: number) => void;
}
export interface ToolbarAction {
    key: string;
    label: string;
    icon?: string;
    type?: 'primary' | 'default' | 'danger';
    disabled?: boolean;
    onClick?: () => void;
}
export interface SearchField {
    key: string;
    label: string;
    type: 'input' | 'select' | 'date' | 'dateRange' | 'combinedSelect' | 'checkbox';
    placeholder?: string;
    options?: Array<{
        label: string;
        value: any;
    }>;
    combinedConfig?: {
        leftOptions?: Array<{
            label: string;
            value: any;
        }>;
        rightOptions?: Array<{
            label: string;
            value: any;
        }>;
    };
    checkboxConfig?: {
        text: string;
        tooltip?: string;
    };
}
export interface GroupTab {
    key: string;
    label: string;
    count?: number;
    active?: boolean;
}
export interface TableViewWithSearchProps extends React.HTMLAttributes<HTMLDivElement> {
    title?: string;
    columns: TableColumn[];
    loading?: boolean;
    componentId?: string;
    events?: Record<string, any>;
    searchFields?: SearchField[];
    showMoreSearchConditions?: boolean;
    onMoreSearchConditions?: () => void;
    toolbarActions?: ToolbarAction[];
    quickSearch?: {
        placeholder?: string;
        onSearch?: (value: string) => void;
    };
    toolbarIcons?: Array<{
        key: string;
        icon: string;
        tooltip?: string;
        onClick?: () => void;
    }>;
    groupTabs?: GroupTab[];
    onGroupTabChange?: (tabKey: string) => void;
    showCancelGroup?: boolean;
    onCancelGroup?: () => void;
    rowActions?: TableAction[];
    pagination?: {
        current: number;
        pageSize: number;
        total: number;
        showSizeChanger?: boolean;
        showQuickJumper?: boolean;
        showTotal?: boolean;
    };
    rowSelection?: {
        type: 'checkbox' | 'radio';
        selectedRowKeys?: string[];
        onChange?: (selectedRowKeys: string[], selectedRows: any[]) => void;
    };
    style?: React.CSSProperties;
    className?: string;
    onSearch?: (searchValues: Record<string, any>) => void;
    onTableChange?: (pagination: any, filters: any, sorter: any) => void;
    onRow?: (record: any, index: number) => React.HTMLAttributes<HTMLTableRowElement>;
}
export declare const TableViewWithSearch: React.FC<TableViewWithSearchProps>;
//# sourceMappingURL=TableViewWithSearch.d.ts.map