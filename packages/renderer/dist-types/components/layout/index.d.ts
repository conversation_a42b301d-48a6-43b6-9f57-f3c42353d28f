import { ComponentMeta } from '../../types';
import { AdminLayout } from './AdminLayout';
import { TopBottomLayout } from './TopBottomLayout';
export { AdminLayout, TopBottomLayout };
export declare const adminLayoutMeta: ComponentMeta;
export declare const topBottomLayoutMeta: ComponentMeta;
export declare const layoutComponentMetas: ComponentMeta[];
export declare const layoutComponents: {
    AdminLayout: import("react").FC<import("./AdminLayout").AdminLayoutProps>;
    TopBottomLayout: import("react").FC<import("./TopBottomLayout").TopBottomLayoutProps>;
};
//# sourceMappingURL=index.d.ts.map