import { Renderer } from './core/Renderer';
export { Renderer } from './core/Renderer';
export { ComponentRegistry } from './core/ComponentRegistry';
export { ApiManager } from './core/ApiManager';
export { EventBus } from './core/EventBus';
export { ThemeManager } from './core/ThemeManager';
export { ComponentDataProvider, useComponentData, useMockData } from './context/ComponentDataContext';
export * from './types';
export * from './utils';
export * from './components/basic';
export * from './components/business';
export * from './components/layout';
export declare const createRenderer: (config?: import("./types").RendererConfig) => Renderer;
export declare const version = "1.0.0";
//# sourceMappingURL=index.d.ts.map