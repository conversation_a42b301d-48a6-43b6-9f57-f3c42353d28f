import React, { ReactNode } from 'react';
import type { ComponentSchema } from '../types';
interface ComponentDataContextType {
    getComponentData: (componentId: string) => ComponentSchema | undefined;
    setComponentData: (componentId: string, data: ComponentSchema) => void;
}
interface ComponentDataProviderProps {
    children: ReactNode;
    components?: Record<string, ComponentSchema>;
}
export declare const ComponentDataProvider: React.FC<ComponentDataProviderProps>;
export declare const useComponentData: () => ComponentDataContextType;
export declare const useMockData: (componentId?: string, apiName?: string) => any[];
export {};
//# sourceMappingURL=ComponentDataContext.d.ts.map