import { ApiConfig } from '../types/events';
export declare const useEventHandler: (componentId?: string, events?: Record<string, any>) => {
    triggerEvent: (eventId: string, eventData?: any) => Promise<any>;
    callApi: (apiConfig: ApiConfig, eventData?: any) => Promise<any>;
    onMount: (data?: any) => Promise<any>;
    onSearch: (data?: any) => Promise<any>;
    onRowClick: (data?: any) => Promise<any>;
    onPageChange: (data?: any) => Promise<any>;
    onToolbarAction: (data?: any) => Promise<any>;
    onNodeClick: (data?: any) => Promise<any>;
    onNodeExpand: (data?: any) => Promise<any>;
    onMenuClick: (data?: any) => Promise<any>;
    onUserMenuClick: (data?: any) => Promise<any>;
    onActionClick: (data?: any) => Promise<any>;
};
//# sourceMappingURL=useEventHandler.d.ts.map