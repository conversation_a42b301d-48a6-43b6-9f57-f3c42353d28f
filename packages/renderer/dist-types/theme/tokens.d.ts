export declare const tokens: {
    readonly color: {
        readonly brand: "var(--lowcode-primary-color, #1677ff)";
        readonly text: "var(--lowcode-text-color, #111827)";
        readonly bg: "var(--lowcode-background-color, #ffffff)";
        readonly border: "var(--lowcode-border-color, #e5e7eb)";
        readonly gray25: "#f8fafc";
        readonly gray50: "#f9fafb";
        readonly gray100: "#f3f4f6";
        readonly gray200: "#e5e7eb";
        readonly gray250: "#eef2f7";
        readonly blue50: "#eff6ff";
        readonly blue100: "#dbeafe";
    };
    readonly radius: {
        readonly sm: 6;
        readonly md: 8;
        readonly lg: 12;
        readonly pill: 9999;
    };
    readonly shadow: {
        readonly xs: "0 1px 2px rgba(0,0,0,0.04)";
        readonly sm: "0 2px 8px rgba(0,0,0,0.08)";
        readonly md: "0 10px 24px rgba(0,0,0,0.12)";
    };
    readonly spacing: {
        readonly xs: 4;
        readonly sm: 6;
        readonly md: 8;
        readonly lg: 12;
        readonly xl: 16;
    };
};
export declare const focusRing: (rgba?: string) => string;
//# sourceMappingURL=tokens.d.ts.map