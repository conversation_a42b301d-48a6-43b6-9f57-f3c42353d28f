import React from 'react';

export interface TopBottomLayoutProps {
  header?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;
  headerHeight?: number;
  footerHeight?: number;
  style?: React.CSSProperties;
  className?: string;
}

export const TopBottomLayout: React.FC<TopBottomLayoutProps> = ({
  header,
  footer,
  children,
  headerHeight = 64,
  footerHeight = 32,
  style,
  className,
}) => {
  const layoutStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    overflow: 'hidden',
    ...style,
  };

  const headerStyle: React.CSSProperties = {
    height: `${headerHeight}px`,
    flexShrink: 0,
    zIndex: 1001,
  };

  const contentStyle: React.CSSProperties = {
    flex: 1,
    overflow: 'auto',
    display: 'flex',
    flexDirection: 'column',
  };

  const footerStyle: React.CSSProperties = {
    height: `${footerHeight}px`,
    flexShrink: 0,
    zIndex: 1000,
  };

  return (
    <div className={`lowcode-top-bottom-layout ${className || ''}`} style={layoutStyle}>
      {/* 顶部区域 */}
      {header && (
        <div style={headerStyle}>
          {header}
        </div>
      )}

      {/* 中间内容区域 */}
      <div style={contentStyle}>
        {children}
      </div>

      {/* 底部区域 */}
      {footer && (
        <div style={footerStyle}>
          {footer}
        </div>
      )}
    </div>
  );
};
