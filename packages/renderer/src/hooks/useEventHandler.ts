import { useCallback } from 'react';
import { ApiConfig } from '../types/events';

// 事件处理hook
export const useEventHandler = (componentId?: string, events?: Record<string, any>) => {
  
  // 调用API的通用方法
  const callApi = useCallback(async (apiConfig: ApiConfig, eventData?: any) => {
    try {
      const { url, method, headers, params, body, timeout = 5000 } = apiConfig;
      
      // 构建完整的URL
      let fullUrl = url;
      
      // 替换URL中的参数占位符，如 /api/user/{id}
      if (eventData && typeof eventData === 'object') {
        Object.keys(eventData).forEach(key => {
          fullUrl = fullUrl.replace(`{${key}}`, encodeURIComponent(eventData[key]));
        });
      }
      
      // 添加查询参数
      if (method === 'GET' && params && Object.keys(params).length > 0) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, String(value));
          }
        });
        fullUrl += (fullUrl.includes('?') ? '&' : '?') + searchParams.toString();
      }
      
      // 构建请求配置
      const requestConfig: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        signal: AbortSignal.timeout(timeout)
      };
      
      // 添加请求体
      if (method !== 'GET' && body) {
        requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
      } else if (method !== 'GET' && eventData) {
        requestConfig.body = JSON.stringify(eventData);
      }
      
      console.log(`[Event API] ${method} ${fullUrl}`, {
        componentId,
        eventData,
        requestConfig
      });
      
      // 发送请求
      const response = await fetch(fullUrl, requestConfig);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log(`[Event API] Response:`, result);
      
      return result;
    } catch (error) {
      console.error(`[Event API] Error:`, error);
      
      // 在开发环境下显示友好的错误提示
      if (process.env.NODE_ENV === 'development') {
        console.warn(`[Event API] 接口调用失败，这在开发环境下是正常的。实际部署时请确保接口可用。`);
      }
      
      throw error;
    }
  }, [componentId]);
  
  // 触发事件的通用方法
  const triggerEvent = useCallback(async (eventId: string, eventData?: any) => {
    if (!events || !events[eventId]) {
      console.warn(`[Event] 组件 ${componentId} 的事件 ${eventId} 未配置`);
      return;
    }
    
    const eventConfig = events[eventId];
    
    if (!eventConfig.apiConfig) {
      console.warn(`[Event] 组件 ${componentId} 的事件 ${eventId} 未配置API`);
      return;
    }
    
    try {
      const result = await callApi(eventConfig.apiConfig, eventData);
      console.log(`[Event] 事件 ${eventId} 执行成功:`, result);
      return result;
    } catch (error) {
      console.error(`[Event] 事件 ${eventId} 执行失败:`, error);
      throw error;
    }
  }, [events, componentId, callApi]);
  
  // 返回事件处理方法
  return {
    triggerEvent,
    callApi,
    
    // 预定义的事件处理方法
    onMount: useCallback((data?: any) => triggerEvent('onMount', data), [triggerEvent]),
    onSearch: useCallback((data?: any) => triggerEvent('onSearch', data), [triggerEvent]),
    onRowClick: useCallback((data?: any) => triggerEvent('onRowClick', data), [triggerEvent]),
    onPageChange: useCallback((data?: any) => triggerEvent('onPageChange', data), [triggerEvent]),
    onToolbarAction: useCallback((data?: any) => triggerEvent('onToolbarAction', data), [triggerEvent]),
    onNodeClick: useCallback((data?: any) => triggerEvent('onNodeClick', data), [triggerEvent]),
    onNodeExpand: useCallback((data?: any) => triggerEvent('onNodeExpand', data), [triggerEvent]),
    onMenuClick: useCallback((data?: any) => triggerEvent('onMenuClick', data), [triggerEvent]),
    onUserMenuClick: useCallback((data?: any) => triggerEvent('onUserMenuClick', data), [triggerEvent]),
    onActionClick: useCallback((data?: any) => triggerEvent('onActionClick', data), [triggerEvent])
  };
};
