// 导入核心类
import { Renderer } from './core/Renderer';

// 导出核心类
export { Renderer } from './core/Renderer';
export { ComponentRegistry } from './core/ComponentRegistry';
export { ApiManager } from './core/ApiManager';
export { EventBus } from './core/EventBus';
export { ThemeManager } from './core/ThemeManager';

// 导出Context
export { ComponentDataProvider, useComponentData, useMockData } from './context/ComponentDataContext';

// 导出类型定义
export * from './types';

// 导出工具函数
export * from './utils';

// 导出基础组件
export * from './components/basic';

// 导出业务组件
export * from './components/business';

// 导出布局组件
export * from './components/layout';

// 创建默认渲染器实例
export const createRenderer = (config?: import('./types').RendererConfig) => {
  return new Renderer(config);
};

// 版本信息
export const version = '1.0.0';
