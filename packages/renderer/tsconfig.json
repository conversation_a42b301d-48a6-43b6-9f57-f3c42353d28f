{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "composite": true, "outDir": "./dist-types", "rootDir": "./src", "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "sourceMap": false, "noEmit": false, "allowImportingTsExtensions": false}, "include": ["src/**/*"], "exclude": ["dist", "dist-types", "node_modules"]}